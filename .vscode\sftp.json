{"name": "MCL Curta - Deploy Automático", "host": "br.brasil111-7590.com.br", "protocol": "ftp", "port": 21, "username": "<EMAIL>", "password": "augment@123", "remotePath": "/home/<USER>/mcl.kleubersilva.com.br", "localPath": "./", "uploadOnSave": true, "useTempFile": false, "openSsh": false, "downloadOnOpen": false, "delete": true, "ignore": [".vscode", ".git", ".DS_Store", "node_modules", "*.log", "KS.MD", "README.md"], "watcher": {"files": "**/*", "autoUpload": true, "autoDelete": true}, "syncOption": {"delete": true, "skipCreate": false, "ignoreExisting": false, "update": true}, "concurrency": 4, "connectTimeout": 10000, "interactiveAuth": false, "algorithms": {"kex": [], "cipher": [], "serverHostKey": [], "hmac": []}}