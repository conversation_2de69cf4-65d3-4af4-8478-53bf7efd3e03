Write-Host "Enviando MCL Curta V2 CORRIGIDA para mcl.kleubersilva.com.br/v2..." -ForegroundColor Green

# Script FTP para atualizar arquivos da v2
$ftpScript = @"
open br.brasil111-7590.com.br
<EMAIL>
augment@123
binary
cd v2
put v2\index.html index.html
cd assets
cd js
put v2\assets\js\main.js main.js
quit
"@

$ftpScript | Out-File -FilePath "ftp_v2_update.txt" -Encoding ASCII
ftp -s:ftp_v2_update.txt
Remove-Item "ftp_v2_update.txt" -ErrorAction SilentlyContinue

Write-Host "V2 CORRIGIDA enviada com sucesso! Acesse: https://mcl.kleubersilva.com.br/v2" -ForegroundColor Green
Write-Host "Correções aplicadas:" -ForegroundColor Yellow
Write-Host "✓ CSS complexo convertido para inline styles" -ForegroundColor White
Write-Host "✓ Conflito de variáveis JavaScript corrigido" -ForegroundColor White
Write-Host "✓ CTA button final corrigido para Typeform" -ForegroundColor White
Write-Host "✓ Compatibilidade com browsers melhorada" -ForegroundColor White
Write-Host "✓ Renderização de texto e imagens otimizada" -ForegroundColor White
Write-Host "✓ Todos os elementos devem estar visíveis agora" -ForegroundColor White
