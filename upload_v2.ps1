# Script para upload FTP da V2 otimizada
$ftpServer = "ftp://br.brasil111-7590.com.br"
$username = "kleubers"
$remotePath = "/home/<USER>/mcl.kleubersilva.com.br/v2"

# Função para upload FTP
function Upload-File {
    param($localFile, $remoteFile)
    
    try {
        $webclient = New-Object System.Net.WebClient
        $webclient.Credentials = New-Object System.Net.NetworkCredential($username, $password)
        $uri = "$ftpServer$remoteFile"
        $webclient.UploadFile($uri, $localFile)
        Write-Host "✅ Upload realizado: $remoteFile"
        return $true
    }
    catch {
        Write-Host "❌ Erro no upload: $($_.Exception.Message)"
        return $false
    }
    finally {
        if ($webclient) { $webclient.Dispose() }
    }
}

# Solicitar senha
$password = Read-Host "Digite a senha FTP" -AsSecureString
$password = [Runtime.InteropServices.Marshal]::PtrTo<PERSON>tring<PERSON>uto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))

Write-Host "🚀 Iniciando upload da V2 otimizada..."

# Upload dos arquivos
$uploads = @(
    @{Local="v2\index.html"; Remote="$remotePath/index.html"},
    @{Local="v2\assets\js\main.js"; Remote="$remotePath/assets/js/main.js"}
)

$success = $true
foreach ($upload in $uploads) {
    if (-not (Upload-File $upload.Local $upload.Remote)) {
        $success = $false
    }
}

if ($success) {
    Write-Host "✅ Deploy concluído com sucesso!"
    Write-Host "🌐 Página disponível em: https://mcl.kleubersilva.com.br/v2/"
} else {
    Write-Host "❌ Alguns arquivos falharam no upload"
}