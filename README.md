# MCL Curta - Landing Page Profissional

Uma landing page moderna e responsiva para a **Mentoria Continuada em Liderança** do <PERSON><PERSON><PERSON>, desenvolvida com foco em conversão, performance e experiência do usuário.

## 🎯 Características Principais

### Design & Identidade Visual
- **Paleta de cores**: Base neutra (branco, cinzas, grafite) + la<PERSON><PERSON> da marca (#F97316/#EA580C)
- **Tipografia**: Inter (Google Fonts) - pesos 400/600/700
- **Tom**: Minimalista, corporativo, limpo com respiro generoso
- **Elementos**: Cards com sombras leves, bordas arredondadas (6-8px)

### Funcionalidades Técnicas
- ✅ **Responsivo**: Mobile-first, otimizado para 360px+
- ✅ **Performance**: Tailwind CSS via CDN, lazy loading, otimizações
- ✅ **Acessibilidade**: Tags semânticas, contraste AAA, navegação por teclado
- ✅ **SEO**: <PERSON><PERSON> tags otimizadas, estrutura semântica
- ✅ **Micro-animações**: Fade-in com Intersection Observer, hover effects

## 📁 Estrutura de Arquivos

```
/
├── index.html              # Landing page principal
├── assets/
│   ├── js/
│   │   └── main.js         # JavaScript para interações
│   └── img/                # Pasta para imagens (vazia)
├── KS.MD                   # Especificações do projeto
└── README.md               # Este arquivo
```

## 🚀 Seções da Landing Page

### 1. Header Sticky
- Logo "MCL Curta" com destaque laranja
- Menu desktop com scroll suave
- Menu hambúrguer responsivo para mobile

### 2. Hero Section
- Headline impactante sobre liderança VAR
- CTA principal "Quero preencher a aplicação — Gratuita"
- Espaço para vídeo YouTube embed (16:9)

### 3. Depoimentos
- Grid responsivo de cards (2 colunas desktop, 1 mobile)
- Ícones de aspas em laranja
- 5 depoimentos reais de alunos

### 4. Liderança Mais Leve
- Seção explicativa sobre equilíbrio vida/trabalho
- CTA secundário

### 5. O que é a Mentoria
- Explicação detalhada do programa
- Benefícios e diferenciais

### 6. Para quem é/não é
- Listas com ícones de check (✓) e X (✗)
- Público-alvo bem definido

### 7. Metodologia
- Destaque para aprendizado "ativo, não passivo"
- Foco em situações reais

### 8. CTA Final
- Seção de conversão principal
- Botão grande com animações
- Indicadores de confiança

### 9. Footer
- Links legais (Política de Privacidade, Cookies)
- Informações da empresa (CNPJ)

## 🛠️ Tecnologias Utilizadas

- **HTML5**: Estrutura semântica
- **Tailwind CSS 3**: Framework CSS via CDN
- **JavaScript Vanilla**: Micro-interações e funcionalidades
- **Google Fonts**: Tipografia Inter
- **Intersection Observer API**: Animações de scroll

## ⚡ Funcionalidades JavaScript

### Menu Mobile
- Toggle do menu hambúrguer
- Animação do ícone (hamburger ↔ X)
- Fechamento automático ao clicar em links

### Scroll Suave
- Navegação suave entre seções
- Compensação automática do header sticky

### Animações
- Fade-in progressivo com Intersection Observer
- Hover effects nos CTAs com movimento de seta
- Efeitos de lift nos cards

### Performance
- Header dinâmico com mudança de opacidade no scroll
- Lazy loading para iframes
- Preload de recursos críticos

### Acessibilidade
- Navegação por teclado (Tab, Esc)
- Indicadores visuais de foco
- Suporte a screen readers

### Analytics
- Tracking de cliques nos CTAs
- Suporte para Google Analytics e Facebook Pixel

## 🎨 Customização

### Cores
As cores principais estão definidas no Tailwind config:
```javascript
colors: {
    'orange': {
        500: '#F97316',  // Laranja principal
        600: '#EA580C',  // Laranja hover
    }
}
```

### Animações
Personalize as animações no CSS customizado:
```css
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}
```

## 📱 Responsividade

### Breakpoints Tailwind
- **sm**: 640px+
- **md**: 768px+
- **lg**: 1024px+
- **xl**: 1280px+

### Mobile-First
- Layout otimizado para 360px mínimo
- Grid responsivo (1 coluna mobile → 2 colunas desktop)
- Menu hambúrguer para telas pequenas

## 🔧 Instalação e Uso

1. **Clone ou baixe os arquivos**
2. **Abra index.html no navegador**
3. **Personalize conforme necessário**

### Para desenvolvimento local:
```bash
# Servir com servidor local (opcional)
python -m http.server 8000
# ou
npx serve .
```

## 📊 Otimizações de Performance

- **CSS**: Tailwind via CDN (sem build necessário)
- **Fonts**: Preload do Google Fonts
- **Images**: Lazy loading implementado
- **JavaScript**: Vanilla JS (sem frameworks pesados)
- **Animations**: Hardware-accelerated transforms

## 🎯 Conversão e UX

### CTAs Estratégicos
- Múltiplos pontos de conversão
- Botões com micro-animações
- Textos persuasivos e urgência

### Prova Social
- 5 depoimentos reais de clientes
- Cargos e empresas mencionados
- Layout profissional e confiável

### Fluxo de Navegação
- Scroll suave entre seções
- Header sempre visível
- CTAs bem posicionados

## 📝 Manutenção

### Atualizações de Conteúdo
- Textos estão no HTML (fácil edição)
- Depoimentos organizados em cards
- Links de CTA centralizados

### Adição de Imagens
- Coloque imagens na pasta `assets/img/`
- Otimize para web (WebP recomendado)
- Use lazy loading para performance

### Tracking e Analytics
- Configure Google Analytics no JavaScript
- Adicione Facebook Pixel se necessário
- Monitore conversões dos CTAs

## 🚀 Deploy

### Hospedagem Estática
- **Netlify**: Drag & drop da pasta
- **Vercel**: Deploy automático
- **GitHub Pages**: Commit e push
- **Servidor tradicional**: Upload via FTP

### Domínio Personalizado
- Configure DNS para apontar para hospedagem
- Adicione SSL/HTTPS
- Configure redirects se necessário

## 📞 Suporte

Para dúvidas sobre implementação ou customização, consulte:
- Documentação do Tailwind CSS
- MDN Web Docs para JavaScript
- Especificações originais no arquivo `KS.MD`

---

**Desenvolvido com foco em conversão e experiência do usuário** 🎯
