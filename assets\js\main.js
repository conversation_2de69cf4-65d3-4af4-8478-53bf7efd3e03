/**
 * MCL Curta - Landing Page JavaScript
 * Micro-interações e funcionalidades
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ========================================
    // MOBILE MENU TOGGLE
    // ========================================
    const menuBtn = document.getElementById('menuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    
    if (menuBtn && mobileMenu) {
        menuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            
            // Animação do ícone do menu (hamburger para X)
            const icon = menuBtn.querySelector('svg');
            if (mobileMenu.classList.contains('hidden')) {
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
            } else {
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
            }
        });
        
        // Fechar menu ao clicar em um link
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
                const icon = menuBtn.querySelector('svg');
                icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
            });
        });
    }
    
    // ========================================
    // SMOOTH SCROLL PARA ANCHORS
    // ========================================
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Ignorar links vazios ou apenas #
            if (href === '#' || href === '') {
                return;
            }
            
            const targetElement = document.querySelector(href);
            
            if (targetElement) {
                e.preventDefault();
                
                // Calcular offset para compensar header sticky
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // ========================================
    // INTERSECTION OBSERVER - FADE IN ANIMATIONS
    // ========================================
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-visible');
                // Opcional: parar de observar após animação
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observar todos os elementos com classe fade-in
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach(element => {
        observer.observe(element);
    });
    
    // ========================================
    // ANIMAÇÕES DE HOVER PARA CTAs
    // ========================================
    const ctaButtons = document.querySelectorAll('.btn-hover');
    
    ctaButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            const arrow = this.querySelector('svg');
            if (arrow) {
                arrow.style.transform = 'translateX(4px)';
                arrow.style.transition = 'transform 0.2s ease';
            }
        });
        
        button.addEventListener('mouseleave', function() {
            const arrow = this.querySelector('svg');
            if (arrow) {
                arrow.style.transform = 'translateX(0)';
            }
        });
    });
    
    // ========================================
    // HEADER BACKGROUND ON SCROLL
    // ========================================
    const header = document.querySelector('header');
    let lastScrollY = window.scrollY;
    
    function updateHeaderOnScroll() {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > 100) {
            header.classList.add('shadow-lg');
            header.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
        } else {
            header.classList.remove('shadow-lg');
            header.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        }
        
        lastScrollY = currentScrollY;
    }
    
    // Throttle scroll events para performance
    let ticking = false;
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateHeaderOnScroll);
            ticking = true;
            setTimeout(() => { ticking = false; }, 16); // ~60fps
        }
    }
    
    window.addEventListener('scroll', requestTick);
    
    // ========================================
    // LAZY LOADING PARA IFRAME (YouTube)
    // ========================================
    const iframes = document.querySelectorAll('iframe[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const iframeObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const iframe = entry.target;
                    if (iframe.dataset.src) {
                        iframe.src = iframe.dataset.src;
                        iframe.removeAttribute('data-src');
                    }
                    iframeObserver.unobserve(iframe);
                }
            });
        });
        
        iframes.forEach(iframe => {
            iframeObserver.observe(iframe);
        });
    }
    
    // ========================================
    // ANALYTICS E TRACKING (OPCIONAL)
    // ========================================
    
    // Tracking de cliques nos CTAs
    const ctaLinks = document.querySelectorAll('a[href*="typeform.com"]');
    
    ctaLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Google Analytics ou outro sistema de tracking
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    'event_category': 'CTA',
                    'event_label': 'Aplicacao MCL Curta',
                    'value': 1
                });
            }
            
            // Facebook Pixel ou outro sistema
            if (typeof fbq !== 'undefined') {
                fbq('track', 'Lead');
            }
            
            console.log('CTA clicked: Aplicação MCL Curta');
        });
    });
    
    // ========================================
    // PERFORMANCE OPTIMIZATIONS
    // ========================================
    
    // Preload de recursos críticos
    function preloadCriticalResources() {
        // Preload da fonte se não estiver carregada
        if (!document.querySelector('link[href*="fonts.googleapis.com"]')) {
            const fontLink = document.createElement('link');
            fontLink.rel = 'preload';
            fontLink.as = 'style';
            fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap';
            document.head.appendChild(fontLink);
        }
    }
    
    // Executar otimizações após carregamento
    if (document.readyState === 'complete') {
        preloadCriticalResources();
    } else {
        window.addEventListener('load', preloadCriticalResources);
    }
    
    // ========================================
    // ACCESSIBILITY IMPROVEMENTS
    // ========================================
    
    // Melhorar navegação por teclado
    document.addEventListener('keydown', function(e) {
        // ESC para fechar menu mobile
        if (e.key === 'Escape' && mobileMenu && !mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
            const icon = menuBtn.querySelector('svg');
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
            menuBtn.focus();
        }
    });
    
    // Indicador visual de foco para navegação por teclado
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
    
    // ========================================
    // CONSOLE LOG PARA DEBUG
    // ========================================
    console.log('MCL Curta - Landing Page carregada com sucesso! 🚀');
    console.log('Funcionalidades ativas:');
    console.log('✓ Menu mobile responsivo');
    console.log('✓ Scroll suave para anchors');
    console.log('✓ Animações fade-in com Intersection Observer');
    console.log('✓ Micro-animações de hover');
    console.log('✓ Header dinâmico no scroll');
    console.log('✓ Lazy loading para iframes');
    console.log('✓ Tracking de CTAs');
    console.log('✓ Melhorias de acessibilidade');
});
