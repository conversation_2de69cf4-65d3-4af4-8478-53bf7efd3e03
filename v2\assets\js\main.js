/**
 * MCL Curta V2 - Landing Page JavaScript
 * Micro-interações e funcionalidades
 */

document.addEventListener('DOMContentLoaded', function() {

    // ========================================
    // INTERSECTION OBSERVER - FADE IN ANIMATIONS
    // ========================================
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-visible');
                // Opcional: parar de observar após animação
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observar todos os elementos com classe fade-in
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach(element => {
        observer.observe(element);
    });

    // ========================================
    // CTA BUTTON TRACKING
    // ========================================
    const ctaButtons = document.querySelectorAll('a[href*="typeform.com"]');

    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Tracking de clique no CTA
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    'event_category': 'CTA',
                    'event_label': 'Typeform Application',
                    'value': 1
                });
            }

            if (typeof fbq !== 'undefined') {
                fbq('track', 'InitiateCheckout', {
                    content_name: 'MCL Curta V2 Application',
                    content_category: 'Leadership Mentoring'
                });
            }

            console.log('CTA clicked: Redirecting to Typeform application');
        });
    });

    // ========================================
    // SMOOTH SCROLL PARA ANCHORS
    // ========================================
    const anchorLinks = document.querySelectorAll('a[href^="#"]');

    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Ignorar links vazios ou apenas #
            if (href === '#' || href === '') {
                return;
            }

            const targetElement = document.querySelector(href);

            if (targetElement) {
                e.preventDefault();

                // Scroll suave para o elemento
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // ========================================
    // ANIMAÇÕES DE HOVER PARA CTAs
    // ========================================
    const ctaButtons = document.querySelectorAll('.btn-hover');

    ctaButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            const arrow = this.querySelector('svg');
            if (arrow) {
                arrow.style.transform = 'translateX(4px)';
                arrow.style.transition = 'transform 0.2s ease';
            }
        });

        button.addEventListener('mouseleave', function() {
            const arrow = this.querySelector('svg');
            if (arrow) {
                arrow.style.transform = 'translateX(0)';
            }
        });
    });

    // ========================================
    // ENHANCED VISUAL INTERACTIONS
    // ========================================

    // Add parallax effect to background images
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('[class*="bg-gradient"]');

        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });

    // Enhanced hover effects for cards
    const cards = document.querySelectorAll('.shadow-lg, .shadow-xl');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.transition = 'all 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // ========================================
    // LAZY LOADING PARA IFRAME (YouTube)
    // ========================================
    const iframes = document.querySelectorAll('iframe[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const iframeObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const iframe = entry.target;
                    if (iframe.dataset.src) {
                        iframe.src = iframe.dataset.src;
                        iframe.removeAttribute('data-src');
                    }
                    iframeObserver.unobserve(iframe);
                }
            });
        });

        iframes.forEach(iframe => {
            iframeObserver.observe(iframe);
        });
    }

    // ========================================
    // ANALYTICS E TRACKING
    // ========================================

    // Tracking de scroll depth
    let maxScroll = 0;
    const trackingPoints = [25, 50, 75, 100];
    const trackedPoints = new Set();

    function trackScrollDepth() {
        const scrollPercent = Math.round((window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100);

        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;

            trackingPoints.forEach(point => {
                if (scrollPercent >= point && !trackedPoints.has(point)) {
                    trackedPoints.add(point);

                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'scroll', {
                            'event_category': 'engagement',
                            'event_label': `${point}%`,
                            'value': point
                        });
                    }

                    console.log(`Scroll depth: ${point}%`);
                }
            });
        }
    }

    // Throttle scroll events
    let scrollTicking = false;

    function requestScrollTick() {
        if (!scrollTicking) {
            requestAnimationFrame(trackScrollDepth);
            scrollTicking = true;
            setTimeout(() => { scrollTicking = false; }, 100);
        }
    }

    window.addEventListener('scroll', requestScrollTick);

    // ========================================
    // CONSOLE LOG PARA DEBUG
    // ========================================
    console.log('MCL Curta V2 - Landing Page carregada com sucesso! 🚀');
    console.log('Funcionalidades ativas:');
    console.log('✓ Formulário de lead com validação');
    console.log('✓ Scroll suave para anchors');
    console.log('✓ Animações fade-in com Intersection Observer');
    console.log('✓ Micro-animações de hover');
    console.log('✓ Formatação automática de telefone');
    console.log('✓ Lazy loading para iframes');
    console.log('✓ Tracking de conversões e scroll depth');
});
