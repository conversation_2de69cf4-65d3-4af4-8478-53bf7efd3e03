/**
 * MCL Curta V2 - Landing Page JavaScript
 * Micro-interações e funcionalidades
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ========================================
    // INTERSECTION OBSERVER - FADE IN ANIMATIONS
    // ========================================
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-visible');
                // Opcional: parar de observar após animação
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observar todos os elementos com classe fade-in
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach(element => {
        observer.observe(element);
    });
    
    // ========================================
    // FORM HANDLING
    // ========================================
    const leadForm = document.getElementById('leadForm');
    
    if (leadForm) {
        leadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Coletar dados do formulário
            const formData = new FormData(leadForm);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                position: formData.get('position'),
                company_size: formData.get('company_size')
            };
            
            // Validação básica
            if (!data.name || !data.email || !data.phone || !data.position || !data.company_size) {
                alert('Por favor, preencha todos os campos obrigatórios.');
                return;
            }
            
            // Validação de email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                alert('Por favor, insira um email válido.');
                return;
            }
            
            // Validação de telefone (básica)
            const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$|^\d{10,11}$/;
            if (!phoneRegex.test(data.phone.replace(/\D/g, ''))) {
                // Auto-formatar telefone se possível
                const cleanPhone = data.phone.replace(/\D/g, '');
                if (cleanPhone.length === 10 || cleanPhone.length === 11) {
                    // Telefone válido, continuar
                } else {
                    alert('Por favor, insira um telefone válido com DDD.');
                    return;
                }
            }
            
            // Simular envio (aqui você integraria com sua API)
            const submitButton = leadForm.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            
            submitButton.textContent = 'ENVIANDO...';
            submitButton.disabled = true;
            
            // Simular delay de envio
            setTimeout(() => {
                // Aqui você faria a integração real com sua API
                console.log('Dados do lead:', data);
                
                // Tracking de conversão
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'conversion', {
                        'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL',
                        'value': 1.0,
                        'currency': 'BRL'
                    });
                }
                
                if (typeof fbq !== 'undefined') {
                    fbq('track', 'Lead', {
                        content_name: 'MCL Curta V2 Lead Form',
                        content_category: 'Leadership Mentoring'
                    });
                }
                
                // Redirecionar para página de obrigado ou mostrar mensagem
                alert('Obrigado! Sua aplicação foi enviada com sucesso. Entraremos em contato em até 24h.');
                
                // Limpar formulário
                leadForm.reset();
                
                // Restaurar botão
                submitButton.textContent = originalText;
                submitButton.disabled = false;
                
            }, 2000);
        });
    }
    
    // ========================================
    // SMOOTH SCROLL PARA ANCHORS
    // ========================================
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Ignorar links vazios ou apenas #
            if (href === '#' || href === '') {
                return;
            }
            
            const targetElement = document.querySelector(href);
            
            if (targetElement) {
                e.preventDefault();
                
                // Scroll suave para o elemento
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // ========================================
    // ANIMAÇÕES DE HOVER PARA CTAs
    // ========================================
    const ctaButtons = document.querySelectorAll('.btn-hover');
    
    ctaButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            const arrow = this.querySelector('svg');
            if (arrow) {
                arrow.style.transform = 'translateX(4px)';
                arrow.style.transition = 'transform 0.2s ease';
            }
        });
        
        button.addEventListener('mouseleave', function() {
            const arrow = this.querySelector('svg');
            if (arrow) {
                arrow.style.transform = 'translateX(0)';
            }
        });
    });
    
    // ========================================
    // FORMATAÇÃO AUTOMÁTICA DE TELEFONE
    // ========================================
    const phoneInput = document.getElementById('phone');
    
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            
            if (value.length <= 11) {
                if (value.length <= 2) {
                    value = value.replace(/(\d{0,2})/, '($1');
                } else if (value.length <= 6) {
                    value = value.replace(/(\d{2})(\d{0,4})/, '($1) $2');
                } else if (value.length <= 10) {
                    value = value.replace(/(\d{2})(\d{4})(\d{0,4})/, '($1) $2-$3');
                } else {
                    value = value.replace(/(\d{2})(\d{5})(\d{0,4})/, '($1) $2-$3');
                }
            }
            
            e.target.value = value;
        });
    }
    
    // ========================================
    // LAZY LOADING PARA IFRAME (YouTube)
    // ========================================
    const iframes = document.querySelectorAll('iframe[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const iframeObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const iframe = entry.target;
                    if (iframe.dataset.src) {
                        iframe.src = iframe.dataset.src;
                        iframe.removeAttribute('data-src');
                    }
                    iframeObserver.unobserve(iframe);
                }
            });
        });
        
        iframes.forEach(iframe => {
            iframeObserver.observe(iframe);
        });
    }
    
    // ========================================
    // ANALYTICS E TRACKING
    // ========================================
    
    // Tracking de scroll depth
    let maxScroll = 0;
    const trackingPoints = [25, 50, 75, 100];
    const trackedPoints = new Set();
    
    function trackScrollDepth() {
        const scrollPercent = Math.round((window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100);
        
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
            
            trackingPoints.forEach(point => {
                if (scrollPercent >= point && !trackedPoints.has(point)) {
                    trackedPoints.add(point);
                    
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'scroll', {
                            'event_category': 'engagement',
                            'event_label': `${point}%`,
                            'value': point
                        });
                    }
                    
                    console.log(`Scroll depth: ${point}%`);
                }
            });
        }
    }
    
    // Throttle scroll events
    let scrollTicking = false;
    
    function requestScrollTick() {
        if (!scrollTicking) {
            requestAnimationFrame(trackScrollDepth);
            scrollTicking = true;
            setTimeout(() => { scrollTicking = false; }, 100);
        }
    }
    
    window.addEventListener('scroll', requestScrollTick);
    
    // ========================================
    // CONSOLE LOG PARA DEBUG
    // ========================================
    console.log('MCL Curta V2 - Landing Page carregada com sucesso! 🚀');
    console.log('Funcionalidades ativas:');
    console.log('✓ Formulário de lead com validação');
    console.log('✓ Scroll suave para anchors');
    console.log('✓ Animações fade-in com Intersection Observer');
    console.log('✓ Micro-animações de hover');
    console.log('✓ Formatação automática de telefone');
    console.log('✓ Lazy loading para iframes');
    console.log('✓ Tracking de conversões e scroll depth');
});
