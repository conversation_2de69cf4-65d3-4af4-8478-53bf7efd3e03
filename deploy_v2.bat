@echo off
echo 🚀 Fazendo deploy da V2 otimizada...
echo.

REM Criar diretórios no servidor
curl -u kleubers --ftp-create-dirs ftp://br.brasil111-7590.com.br/home/<USER>/mcl.kleubersilva.com.br/v2/

REM Upload do index.html
echo 📄 Uploading index.html...
curl -T "v2\index.html" -u kleubers ftp://br.brasil111-7590.com.br/home/<USER>/mcl.kleubersilva.com.br/v2/

REM Criar diretório assets/js
curl -u kleubers --ftp-create-dirs ftp://br.brasil111-7590.com.br/home/<USER>/mcl.kleubersilva.com.br/v2/assets/js/

REM Upload do main.js
echo 📄 Uploading main.js...
curl -T "v2\assets\js\main.js" -u kleubers ftp://br.brasil111-7590.com.br/home/<USER>/mcl.kleubersilva.com.br/v2/assets/js/

echo.
echo ✅ Deploy concluído!
echo 🌐 Página disponível em: https://mcl.kleubersilva.com.br/v2/
pause