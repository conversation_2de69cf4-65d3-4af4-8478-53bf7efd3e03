🎯 OBJETIVO
Crie uma nova versão totalmente profissional da landing-page hoje publicada em https://kleubersilva.com.br/mcl-curta.
Os textos originais devem ser mantidos (ou levemente ajustados para encaixe), mas todo o layout, tipografia, espaçamento, hierarquia visual, responsividade e micro-animações devem ser refeitos para transmitir alto nível de design e codificação.

1 — Identidade visual & estilo
Item	Diretriz
Paleta	Base neutra (branco #FFFFFF, cinzas #F5F5F5, #E5E5E5, grafite #1F2937) + laranja da marca (#F97316 / #EA580C) para CTAs, ícones e detalhes.
Tipografia	Fonte sans de alta legibilidade e “enterprise” (ex. Inter, Work Sans, IBM Plex Sans). Peso 400/600/700.
Tom geral	Minimalista, corporativo, limpo, com respiro generoso. Cards, sombras leves (elevation 1-3), bordas arredondadas (6-8 px).
Micro-animações	Transições de 200-300 ms em hovers; fade-in/slide-up suave quando seções entram no viewport (Intersection Observer).

2 — Estrutura de páginas/arquivos
stack: HTML5 + Tailwind CSS 3 (via CDN) + JavaScript vanilla.

arquitetura:

bash
Copiar
Editar
/index.html          (landing principal)
/assets/
    /img/            (otimize e comprima as imagens)
    /js/main.js      (Intersection Observer, toggle menu)
componentização: separe em partials reutilizáveis (header, footer, card-depoimento, section-cta) para fácil manutenção.

3 — Layout & seções (em ordem sugerida)
Header sticky

Logotipo textual: “MCL <span class='text-orange-500'>Curta</span>”.

Menu desktop (scroll suave com anchors) e hambúrguer mobile.

Hero (split: texto + vídeo YouTube embed 16:9)

Headline igual ao original; CTA primário “Quero preencher a aplicação — Gratuita”.

Depoimentos

Grid responsivo de cards (2 colunas desktop, 1 coluna mobile).

Ícone de aspas em laranja sutil; texto em itálico.

O que é a mentoria & Para quem é / não é

Dois blocos alternando fundo branco / cinza-claro.

Utilize listas com ícones de check (laranja) e x (cinza-500).

Metodologia / Resultados

Fundo branco; parágrafos curtos; destaque termos “ativo, não passivo” em bold.

Seção CTA final

Fundo cinza-100; texto centralizado; botão grande com sombra, animação de “press”.

Footer

Links de política, CNPJ e direitos autorais em texto-cinza-400.

4 — Responsividade & boas práticas
Requisito	Como implementar
Mobile-first absoluto	Otimize para 360 px ≥; depois use md: & lg:.
Performance	Importe apenas Tailwind (CDN) e um preload para a fonte; compacte imagens; lazy-load do iframe YouTube (loading="lazy").
Acessibilidade	Tags semânticas, aria-label nos botões, contraste AAA para texto > 16 px, foco visível.
SEO	<title> descritivo, meta description ≤ 160 caracteres, heading hierarchy correta (h1 único).

5 — Micro-interações (JS em /assets/js/main.js)
pseudo
Copiar
Editar
• Toggle menu mobile (#menuBtn → #mobileMenu.classList.toggle('hidden'))
• IntersectionObserver: adiciona classe `.fade-in-visible` ao chegar 10 % no viewport.
• Animação hover CTA: translate-x + SVG seta em 100 ms.
6 — Instruções finais para geração
Produza o código completo (HTML com Tailwind classes) sem placeholders de lorem, já embutindo os textos atuais.

Otimize imagens: se necessário, adicione observação “substituir por imagem otimizada” com comentários <!-- TODO -->.

Entregue tudo em um único snippet copiado abaixo (Augment dividirá em arquivos).

Após gerar, faça lint automático.

Resultado esperado: arquivo index.html + assets/js/main.js que, ao abrir no navegador, entregue uma landing-page visualmente moderna, alinhada à identidade laranja da marca, carregando rápida e perfeita em mobile.