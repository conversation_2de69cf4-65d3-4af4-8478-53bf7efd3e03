# Script de Deploy Automático para MCL Curta
# Kleuber Silva - mcl.kleubersilva.com.br

param(
    [string]$File = "index.html",
    [switch]$All = $false,
    [switch]$JS = $false
)

Write-Host "🚀 Iniciando deploy para mcl.kleubersilva.com.br..." -ForegroundColor Green

# Configurações FTP
$ftpServer = "br.brasil111-7590.com.br"
$ftpUser = "<EMAIL>"
$ftpPass = "augment@123"

function Upload-File {
    param([string]$LocalFile, [string]$RemoteFile = $LocalFile)
    
    Write-Host "📤 Enviando: $LocalFile" -ForegroundColor Yellow
    
    # Criar script FTP temporário
    $ftpScript = @"
open $ftpServer
$ftpUser
$ftpPass
binary
put $LocalFile $RemoteFile
quit
"@
    
    $ftpScript | Out-File -FilePath "temp_ftp.txt" -Encoding ASCII
    
    # Executar upload
    $result = ftp -s:temp_ftp.txt
    
    # Limpar arquivo temporário
    Remove-Item "temp_ftp.txt" -ErrorAction SilentlyContinue
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $LocalFile enviado com sucesso!" -ForegroundColor Green
    } else {
        Write-Host "❌ Erro ao enviar $LocalFile" -ForegroundColor Red
    }
}

function Upload-Directory {
    param([string]$LocalDir, [string]$RemoteDir = $LocalDir)
    
    Write-Host "📁 Enviando diretório: $LocalDir" -ForegroundColor Yellow
    
    # Criar script FTP para diretório
    $ftpScript = @"
open $ftpServer
$ftpUser
$ftpPass
binary
"@
    
    # Adicionar todos os arquivos do diretório
    Get-ChildItem -Path $LocalDir -Recurse -File | ForEach-Object {
        $relativePath = $_.FullName.Substring((Get-Location).Path.Length + 1)
        $remotePath = $relativePath -replace '\\', '/'
        $ftpScript += "`nput `"$($_.FullName)`" `"$remotePath`""
    }
    
    $ftpScript += "`nquit"
    
    $ftpScript | Out-File -FilePath "temp_ftp_dir.txt" -Encoding ASCII
    
    # Executar upload
    ftp -s:temp_ftp_dir.txt
    
    # Limpar arquivo temporário
    Remove-Item "temp_ftp_dir.txt" -ErrorAction SilentlyContinue
    
    Write-Host "✅ Diretório $LocalDir enviado!" -ForegroundColor Green
}

# Executar upload baseado nos parâmetros
if ($All) {
    Write-Host "📦 Upload completo do projeto..." -ForegroundColor Cyan
    Upload-File "index.html"
    Upload-Directory "assets"
} elseif ($JS) {
    Write-Host "📜 Upload apenas do JavaScript..." -ForegroundColor Cyan
    Upload-File "assets\js\main.js" "assets/js/main.js"
} else {
    Write-Host "📄 Upload do arquivo: $File" -ForegroundColor Cyan
    Upload-File $File
}

Write-Host ""
Write-Host "🌐 Site atualizado: https://mcl.kleubersilva.com.br" -ForegroundColor Green
Write-Host "✨ Deploy concluído com sucesso!" -ForegroundColor Green
